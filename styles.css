/* Stripe Auto Fill 插件样式 */
#stripe-auto-fill-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    backdrop-filter: blur(10px);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

#close-panel {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

#close-panel:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.panel-content {
    padding: 16px;
}

.action-btn {
    width: 100%;
    padding: 12px;
    margin-bottom: 10px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #50e3c2 0%, #4facfe 100%);
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.action-btn:active {
    transform: translateY(0);
}

.status {
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    color: #666;
    border: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #stripe-auto-fill-panel {
        width: 90%;
        right: 5%;
        top: 10px;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    #stripe-auto-fill-panel {
        background: rgba(28, 28, 30, 0.95);
        border-color: #333;
    }
    
    .panel-header {
        border-bottom-color: #333;
    }
    
    .status {
        background: #2c2c2e;
        color: #e0e0e0;
        border-color: #444;
    }
}
