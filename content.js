// Stripe Auto Fill Content Script
(function() {
    'use strict';

    console.log('Stripe Auto Fill 插件已启动');

    // 预设的卡信息
    const CARD_INFO = {
        cardNumber: '****************',
        expiryMonth: '06',
        expiryYear: '28',
        cvc: '124',
        name: 'Canestro',
        country: 'CA',
        address: '2225 137th Avenue',
        city: 'Edmonton',
        state: 'Alberta',
        postalCode: 'T5J 3P4'
    };

    // 全局状态变量
    let captchaDetectorInitialized = false;

    // 工具函数
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async function setNativeValue(element, value) {
        if (!element) return;

        const valueSetter = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(element), 'value')?.set;
        if (valueSetter) {
            valueSetter.call(element, value);
        } else {
            element.value = value;
        }

        // 触发各种事件以确保表单识别输入
        ['input', 'change', 'blur', 'keyup'].forEach(eventType => {
            element.dispatchEvent(new Event(eventType, { bubbles: true }));
        });

        await delay(100);
    }

    // 专门处理下拉框选择
    async function setSelectValue(element, value, searchText = null) {
        if (!element) return false;

        // 如果是select元素
        if (element.tagName === 'SELECT') {
            // 尝试直接设置值
            if (element.querySelector(`option[value="${value}"]`)) {
                await setNativeValue(element, value);
                return true;
            }

            // 尝试通过文本内容查找
            const options = Array.from(element.options);
            const targetTexts = [value, searchText].filter(Boolean);

            for (const text of targetTexts) {
                const option = options.find(opt =>
                    opt.textContent.toLowerCase().includes(text.toLowerCase()) ||
                    opt.value.toLowerCase().includes(text.toLowerCase())
                );
                if (option) {
                    await setNativeValue(element, option.value);
                    return true;
                }
            }
        }

        // 如果是其他类型的下拉框（如自定义组件）
        // 尝试点击展开下拉框
        element.click();
        await delay(200);

        // 查找下拉选项
        const dropdownSelectors = [
            `[data-value="${value}"]`,
            `[value="${value}"]`
        ];

        for (const selector of dropdownSelectors) {
            try {
                const option = document.querySelector(selector);
                if (option) {
                    option.click();
                    await delay(100);
                    return true;
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }

        // 尝试通过文本内容查找选项
        const searchTexts = [value, searchText].filter(Boolean);
        const allElements = document.querySelectorAll('li, div, span, option, [role="option"]');

        for (const searchText of searchTexts) {
            for (const el of allElements) {
                if (el.textContent &&
                    (el.textContent.toLowerCase().includes(searchText.toLowerCase()) ||
                     el.textContent.toLowerCase() === searchText.toLowerCase())) {
                    el.click();
                    await delay(100);
                    return true;
                }
            }
        }

        return false;
    }

    // 获取邮箱输入框
    function getEmailInput() {
        const selectors = [
            'input[type="email"]',
            '#email',
            'input[name="email"]',
            'input[placeholder*="email" i]',
            'input[placeholder*="邮箱" i]'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        // 尝试使用XPath
        try {
            const xpath = "/html/body/div[1]/div/div/div[2]/main/div/form/div[1]/div/div/div[1]/div[1]/div/div/div/div/div[2]/div/div[1]";
            const xpathResult = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            return xpathResult.singleNodeValue;
        } catch (e) {
            console.error("使用XPath查找邮箱输入框失败:", e);
        }
        
        return null;
    }

    // 自动选择银行卡支付选项
    async function selectCardPaymentOption() {
        console.log('1. 点击银行卡选项...');

        try {
            // 查找包含"card"的data-testid属性的元素
            const allElementsWithTestId = document.querySelectorAll('[data-testid]');
            console.log(`找到 ${allElementsWithTestId.length} 个带有data-testid的元素`);

            // 打印所有data-testid以便调试
            allElementsWithTestId.forEach((element, index) => {
                const testId = element.getAttribute('data-testid');
                console.log(`元素 ${index}: data-testid="${testId}"`);
            });

            let cardOption = null;

            for (const element of allElementsWithTestId) {
                const testId = element.getAttribute('data-testid');
                if (testId && testId.includes('card')) {
                    cardOption = element;
                    console.log(`找到银行卡选项: data-testid="${testId}"`);
                    break;
                }
            }

            if (cardOption) {
                cardOption.click();
                await delay(500); // 等待界面更新
                console.log('✅ 银行卡选项已点击');
                return true;
            } else {
                console.log('❌ 未找到包含"card"的data-testid元素');
                return false;
            }
        } catch (error) {
            console.error('选择银行卡选项时出错:', error);
            return false;
        }
    }

    // 自动填写表单
    async function autoFillForm() {
        console.log('开始自动填写表单...');

        try {
            // 首先尝试选择银行卡支付选项
            await selectCardPaymentOption();

            // 等待一下让界面完全加载
            await delay(1000);
            // 获取邮箱输入框以获取用户名
            const emailInput = getEmailInput();
            let emailName = CARD_INFO.name;
            
            if (emailInput) {
                const emailValue = emailInput.value.trim() || emailInput.innerText.trim();
                if (emailValue) {
                    emailName = emailValue.split('@')[0] || CARD_INFO.name;
                }
            }

            // 主要表单字段映射
            const fieldMappings = [
                { id: 'cardNumber', value: CARD_INFO.cardNumber, label: '卡号' },
                { id: 'cardExpiry', value: `${CARD_INFO.expiryMonth}/${CARD_INFO.expiryYear}`, label: '到期日' },
                { id: 'cardCvc', value: CARD_INFO.cvc, label: 'CVC' },
                { id: 'billingName', value: emailName, label: '姓名' }
            ];

            // 填写主要字段
            for (const mapping of fieldMappings) {
                const element = document.getElementById(mapping.id);
                if (element) {
                    await setNativeValue(element, mapping.value);
                    console.log(`${mapping.label}已填写: ${mapping.value}`);
                } else {
                    console.warn(`未找到字段: ${mapping.id}`);
                }
            }

            // 设置国家（下拉框）
            const countrySelect = document.getElementById('billingCountry');
            if (countrySelect) {
                const countrySet = await setSelectValue(countrySelect, CARD_INFO.country, 'Canada');
                if (countrySet) {
                    console.log(`国家已设置: ${CARD_INFO.country}`);
                } else {
                    console.warn('国家设置失败，尝试备用方法');
                    await setNativeValue(countrySelect, CARD_INFO.country);
                }
            }

            // 设置州/省（下拉框）
            const stateSelect = document.getElementById('billingAdministrativeArea');
            if (stateSelect) {
                // 等待国家选择完成后再设置州/省
                await delay(500);
                const stateSet = await setSelectValue(stateSelect, CARD_INFO.state, 'Alberta');
                if (stateSet) {
                    console.log(`州/省已设置: ${CARD_INFO.state}`);
                } else {
                    console.warn('州/省设置失败，尝试备用方法');
                    await setNativeValue(stateSelect, CARD_INFO.state);
                }
            }

            // 地址信息字段映射（不包括州/省，因为它是下拉框需要特殊处理）
            const addressMappings = [
                { id: 'billingAddressLine1', value: CARD_INFO.address, label: '地址' },
                { id: 'billingLocality', value: CARD_INFO.city, label: '城市' },
                { id: 'billingPostalCode', value: CARD_INFO.postalCode, label: '邮政编码' }
            ];

            // 填写地址信息
            for (const mapping of addressMappings) {
                const element = document.getElementById(mapping.id);
                if (element && mapping.value) {
                    await setNativeValue(element, mapping.value);
                    console.log(`${mapping.label}已填写: ${mapping.value}`);
                }
            }



            console.log('表单填写完成！');

            // 等待3秒让税率计算完成，然后自动提交
            console.log('等待3秒让税率计算完成...');
            await delay(3000);

            // 检查提交按钮是否可用并自动提交
            const submitButton = document.querySelector("button[data-testid='hosted-payment-submit-button']");
            if (submitButton && !submitButton.disabled) {
                console.log('税率计算完成，自动提交表单...');
                submitButton.click();

                // 启动验证码检测器
                if (!captchaDetectorInitialized) {
                    setupCaptchaDetector();
                    captchaDetectorInitialized = true;
                    console.log('验证码检测器已启动');
                }
            } else {
                console.log('提交按钮未找到或仍被禁用，请手动检查');
            }

        } catch (error) {
            console.error('填写表单时出错:', error);
        }
    }

    // 自动提交表单
    function autoSubmit() {
        const submitButton = document.querySelector("button[data-testid='hosted-payment-submit-button']");
        if (submitButton && !submitButton.disabled) {
            console.log('自动点击提交按钮...');
            submitButton.click();
            return true;
        } else {
            console.log('提交按钮未找到或已禁用');
            return false;
        }
    }

    // 验证码检测器
    function setupCaptchaDetector() {
        console.log('🤖 验证码检测器已启动');
        let captchaDetected = false;
        let captchaCompleted = false;
        let processingCard = false;

        function checkAndProcessNext() {
            if (captchaCompleted && !processingCard) {
                processingCard = true;
                console.log('✅ 验证码验证已完成，等待3秒后重新提交...');
                setTimeout(() => {
                    console.log('🔄 重新提交表单...');
                    if (autoSubmit()) {
                        setTimeout(() => {
                            captchaDetected = false;
                            captchaCompleted = false;
                            processingCard = false;
                        }, 2000);
                    }
                }, 3000);
            }
        }

        const captchaObserver = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType !== Node.ELEMENT_NODE) continue;
                        if (isCaptchaElement(node) && !captchaDetected) {
                            console.log('🔍 检测到验证码开始');
                            captchaDetected = true;
                        }
                    }
                }
                if (captchaDetected && mutation.removedNodes.length > 0) {
                    for (const node of mutation.removedNodes) {
                        if (node.nodeType !== Node.ELEMENT_NODE) continue;
                        if (isCaptchaElement(node)) {
                            console.log('👍 验证码框被移除，可能已完成验证');
                            captchaCompleted = true;
                            checkAndProcessNext();
                        }
                    }
                }
            }
        });

        captchaObserver.observe(document.documentElement, { childList: true, subtree: true });
        console.log('✅ 验证码检测器设置完成');
    }

    function isCaptchaElement(node) {
        return node.matches && (
            node.matches('[data-hcaptcha-widget-id], iframe[src*="hcaptcha"], .h-captcha, #h-captcha, iframe[src*="stripe"], [data-stripe-captcha]') ||
            (node.querySelector && node.querySelector('[data-hcaptcha-widget-id], iframe[src*="hcaptcha"], .h-captcha, #h-captcha, iframe[src*="stripe"], [data-stripe-captcha]'))
        );
    }

    // 创建控制面板
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'stripe-auto-fill-panel';
        panel.innerHTML = `
            <div class="panel-header">
                <h3>Stripe Auto Fill</h3>
                <button id="close-panel">×</button>
            </div>
            <div class="panel-content">
                <button id="auto-fill-btn" class="action-btn">自动填写</button>
                <button id="auto-submit-btn" class="action-btn">自动提交</button>
                <div class="status" id="status">准备就绪</div>
            </div>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定事件
        document.getElementById('auto-fill-btn').addEventListener('click', () => {
            document.getElementById('status').textContent = '正在填写...';
            autoFillForm().then(() => {
                document.getElementById('status').textContent = '填写完成';
            });
        });
        
        document.getElementById('auto-submit-btn').addEventListener('click', () => {
            if (autoSubmit()) {
                document.getElementById('status').textContent = '已提交';
                setupCaptchaDetector();
            } else {
                document.getElementById('status').textContent = '提交失败';
            }
        });
        
        document.getElementById('close-panel').addEventListener('click', () => {
            panel.style.display = 'none';
        });
    }

    // 初始化
    function init() {
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        console.log('初始化 Stripe Auto Fill...');

        // 创建控制面板
        createControlPanel();
    }

    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'autoFill') {
            autoFillForm().then(() => {
                sendResponse({success: true});
            }).catch(error => {
                console.error('自动填写失败:', error);
                sendResponse({success: false, error: error.message});
            });
            return true; // 保持消息通道开放
        } else if (request.action === 'autoSubmit') {
            try {
                const success = autoSubmit();
                if (success) {
                    setupCaptchaDetector();
                }
                sendResponse({success: success});
            } catch (error) {
                console.error('自动提交失败:', error);
                sendResponse({success: false, error: error.message});
            }
        }
    });

    // 启动
    init();
})();
