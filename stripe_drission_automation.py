#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 DrissionPage 的 Stripe 支付表单自动填写
包含反机器人检测功能
"""

import os
import sys
import random
import time
import logging
from DrissionPage import ChromiumOptions, ChromiumPage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 完整的卡和地址信息
PAYMENT_INFO = {
    "card_number": "4937 2420 1027 3546",
    "expiry_date": "06/28",
    "cvc": "124",
    "name": "Canestro",
    "country": "Canada",
    "address_line1": "2225 137th Avenue",
    "city": "Edmonton",
    "state": "Alberta",
    "postal_code": "T5J 3P4"
}

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
]

def get_random_user_agent():
    """获取随机用户代理"""
    return random.choice(USER_AGENTS)

def setup_browser_autofill(browser):
    """在浏览器中设置自动填充信息"""
    try:
        logging.info("正在设置浏览器自动填充信息...")

        # 注入自动填充数据到浏览器
        autofill_script = f"""
        // 创建自动填充数据
        const paymentData = {{
            cardNumber: '{PAYMENT_INFO["card_number"]}',
            expiryDate: '{PAYMENT_INFO["expiry_date"]}',
            cvc: '{PAYMENT_INFO["cvc"]}',
            name: '{PAYMENT_INFO["name"]}',
            country: '{PAYMENT_INFO["country"]}',
            addressLine1: '{PAYMENT_INFO["address_line1"]}',
            city: '{PAYMENT_INFO["city"]}',
            state: '{PAYMENT_INFO["state"]}',
            postalCode: '{PAYMENT_INFO["postal_code"]}'
        }};

        // 存储到 sessionStorage 供后续使用
        sessionStorage.setItem('autoFillData', JSON.stringify(paymentData));

        // 创建自动填充函数
        window.autoFillForm = function(element) {{
            const data = JSON.parse(sessionStorage.getItem('autoFillData') || '{{}}');
            const fieldName = (element.name || element.id || element.getAttribute('autocomplete') || '').toLowerCase();
            const fieldType = (element.type || '').toLowerCase();
            const placeholder = (element.placeholder || '').toLowerCase();

            console.log('AutoFill - fieldName:', fieldName, 'fieldType:', fieldType, 'placeholder:', placeholder);
            console.log('AutoFill - data:', data);

            // 根据字段特征自动填充
            if (fieldName.includes('card') || fieldName.includes('number') ||
                element.getAttribute('autocomplete') === 'cc-number' ||
                fieldName === 'cardnumber') {{
                element.value = data.cardNumber;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled card number:', data.cardNumber);
                return true;
            }} else if (fieldName.includes('expiry') || fieldName.includes('exp') ||
                       element.getAttribute('autocomplete') === 'cc-exp' ||
                       fieldName === 'expiry') {{
                element.value = data.expiryDate;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled expiry:', data.expiryDate);
                return true;
            }} else if (fieldName.includes('cvc') || fieldName.includes('cvv') ||
                       element.getAttribute('autocomplete') === 'cc-csc' ||
                       fieldName === 'cvc') {{
                element.value = data.cvc;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled CVC:', data.cvc);
                return true;
            }} else if ((fieldName.includes('name') && !fieldName.includes('card')) ||
                       element.getAttribute('autocomplete') === 'cc-name' ||
                       fieldName === 'cardholder') {{
                element.value = data.name;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled name:', data.name);
                return true;
            }} else if (fieldName.includes('address') ||
                       element.getAttribute('autocomplete') === 'address-line1' ||
                       fieldName === 'address') {{
                element.value = data.addressLine1;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled address:', data.addressLine1);
                return true;
            }} else if (fieldName.includes('city') ||
                       element.getAttribute('autocomplete') === 'address-level2' ||
                       fieldName === 'city') {{
                element.value = data.city;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled city:', data.city);
                return true;
            }} else if (fieldName.includes('state') || fieldName.includes('province') ||
                       element.getAttribute('autocomplete') === 'address-level1' ||
                       fieldName === 'state') {{
                element.value = data.state;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled state:', data.state);
                return true;
            }} else if (fieldName.includes('postal') || fieldName.includes('zip') ||
                       element.getAttribute('autocomplete') === 'postal-code' ||
                       fieldName === 'postal') {{
                element.value = data.postalCode;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled postal:', data.postalCode);
                return true;
            }} else if (fieldName.includes('country') ||
                       element.getAttribute('autocomplete') === 'country' ||
                       fieldName === 'country') {{
                element.value = data.country;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled country:', data.country);
                return true;
            }}
            console.log('No matching field pattern found');
            return false;
        }};

        console.log('自动填充功能已设置');
        """

        browser.run_js(autofill_script)
        logging.info("✅ 浏览器自动填充信息设置完成")
        return True

    except Exception as e:
        logging.error(f"设置自动填充失败: {e}")
        return False

def init_browser_with_patch():
    """初始化带有反检测补丁的浏览器"""
    try:
        logging.info("正在初始化带反检测功能的浏览器...")

        # 获取Chrome选项
        co = ChromiumOptions()

        # 设置随机用户代理
        user_agent = get_random_user_agent()
        co.set_user_agent(user_agent)
        logging.info(f"使用用户代理: {user_agent}")

        # 加载turnstilePatch扩展
        try:
            extension_path = os.path.join("1.打开一个单独的实例网页", "turnstilePatch")

            if os.path.exists(extension_path):
                co.add_extension(extension_path)
                logging.info(f"已加载反检测扩展: {extension_path}")
            else:
                logging.warning("反检测扩展未找到，继续执行...")
        except Exception as e:
            logging.warning(f"加载扩展时出错: {e}")

        # 设置浏览器参数以避免检测
        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        co.set_argument("--disable-blink-features=AutomationControlled")
        co.set_argument("--disable-dev-shm-usage")
        co.set_argument("--no-sandbox")

        # 自动选择端口
        co.auto_port()

        # 创建浏览器实例
        browser = ChromiumPage(co)

        # 执行额外的反检测脚本
        browser.run_js("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 随机化鼠标事件
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            EventTarget.prototype.addEventListener = function(type, listener, options) {
                if (type === 'mousemove') {
                    const wrappedListener = function(e) {
                        // 添加轻微的随机延迟
                        setTimeout(() => listener.call(this, e), Math.random() * 2);
                    };
                    return originalAddEventListener.call(this, type, wrappedListener, options);
                }
                return originalAddEventListener.call(this, type, listener, options);
            };
        """)

        logging.info("浏览器初始化成功!")
        return browser

    except Exception as e:
        logging.error(f"浏览器初始化失败: {str(e)}")
        raise

def human_like_delay(min_delay=0.5, max_delay=2.0):
    """模拟人类操作的随机延迟"""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def human_like_type(element, text, typing_delay=0.1):
    """模拟人类打字"""
    element.clear()
    human_like_delay(0.2, 0.5)

    for char in text:
        element.input(char)
        time.sleep(random.uniform(0.05, typing_delay))

def trigger_autofill(browser, element):
    """触发浏览器自动填充功能"""
    try:
        # 点击输入框以获得焦点
        element.click()
        time.sleep(0.1)  # 快速延迟

        # 使用我们注入的自动填充函数
        result = browser.run_js("""
            const element = arguments[0];
            if (window.autoFillForm && typeof window.autoFillForm === 'function') {
                const success = window.autoFillForm(element);
                return {success: success, value: element.value};
            }
            return {success: false, value: ''};
        """, element)

        if result and result.get('success'):
            filled_value = result.get('value', '')
            logging.info(f"✅ 自动填充成功: {filled_value}")
            return True

        return False
    except Exception as e:
        logging.warning(f"触发自动填充失败: {e}")
        return False

def try_autofill_or_manual(browser, element, fallback_text, field_name):
    """尝试自动填充，失败则手动填写"""
    logging.info(f"尝试自动填充 {field_name}...")

    # 首先尝试触发自动填充
    if trigger_autofill(browser, element):
        # 快速检查是否成功填充
        time.sleep(0.1)
        current_value = element.attr('value') or element.text
        if current_value and current_value.strip():
            logging.info(f"✅ {field_name} 自动填充成功: {current_value}")
            return True

    # 自动填充失败，使用快速手动填写
    logging.info(f"自动填充失败，使用手动填写 {field_name}...")

    # 快速输入，减少打字延迟
    element.clear()
    time.sleep(0.05)
    for char in fallback_text:
        element.input(char)
        time.sleep(0.02)  # 非常快的打字速度

    logging.info(f"✅ {field_name} 手动填写完成")
    return True

def fill_stripe_form_drission(browser):
    """使用DrissionPage填写Stripe表单"""
    logging.info("=== 开始填写 Stripe 表单 ===")
    
    # 快速等待页面加载
    time.sleep(2)

    try:
        # 步骤1: 点击银行卡选项
        logging.info("1. 点击银行卡选项...")
        card_option = browser.ele('css:[data-testid*="card"]', timeout=10)
        if card_option:
            card_option.click()
            time.sleep(0.5)  # 快速延迟
            logging.info("✅ 银行卡选项已点击")
        else:
            logging.error("❌ 未找到银行卡选项")
            return False
        
        # 步骤2: 选择加拿大
        logging.info("2. 选择国家为加拿大...")
        country_select = browser.ele('xpath://select[contains(@name, "Country")]', timeout=10)
        if country_select:
            country_select.select.by_text("加拿大")
            time.sleep(0.3)  # 快速延迟
            logging.info("✅ 国家已选择为加拿大")
        else:
            logging.error("❌ 未找到国家选择器")
            return False

        # 步骤3: 选择州/省
        logging.info("3. 选择州/省...")
        state_select = browser.ele('#billingAdministrativeArea', timeout=10)
        if state_select:
            state_select.select.by_text("Alberta")
            time.sleep(0.3)  # 快速延迟
            logging.info("✅ 州/省已选择为Alberta")
        else:
            logging.warning("⚠️ 未找到州/省选择器")

        # 步骤4: 填写城市（尝试自动填充）
        logging.info("4. 填写城市...")
        city_input = browser.ele('#billingLocality', timeout=10)
        if city_input:
            try_autofill_or_manual(browser, city_input, PAYMENT_INFO["city"], "城市")
            time.sleep(0.1)  # 快速延迟

        # 步骤5: 填写邮编（尝试自动填充）
        logging.info("5. 填写邮编...")
        postal_input = browser.ele('#billingPostalCode', timeout=10)
        if postal_input:
            try_autofill_or_manual(browser, postal_input, PAYMENT_INFO["postal_code"], "邮编")

        # 步骤6: 填写地址第1行（尝试自动填充）
        logging.info("6. 填写地址第1行...")
        address_input = browser.ele('#billingAddressLine1', timeout=10)
        if address_input:
            try_autofill_or_manual(browser, address_input, PAYMENT_INFO["address_line1"], "地址第1行")

        # 步骤7: 填写卡信息（尝试自动填充）
        logging.info("7. 填写卡信息...")

        # 卡号（最重要的字段，优先尝试自动填充）
        card_number_input = browser.ele('#cardNumber', timeout=10)
        if card_number_input:
            try_autofill_or_manual(browser, card_number_input, PAYMENT_INFO["card_number"], "卡号")

        # 有效期
        expiry_input = browser.ele('#cardExpiry', timeout=10)
        if expiry_input:
            try_autofill_or_manual(browser, expiry_input, PAYMENT_INFO["expiry_date"], "有效期")

        # CVC
        cvc_input = browser.ele('#cardCvc', timeout=10)
        if cvc_input:
            try_autofill_or_manual(browser, cvc_input, PAYMENT_INFO["cvc"], "CVC")

        # 步骤8: 填写姓名（尝试自动填充）
        logging.info("8. 填写姓名...")

        # 姓名
        name_input = browser.ele('#billingName', timeout=10)
        if name_input:
            try_autofill_or_manual(browser, name_input, PAYMENT_INFO["name"], "姓名")

        # 步骤9: 点击开始试用按钮
        logging.info("9. 点击开始试用按钮...")
        time.sleep(0.5)  # 快速延迟

        # 直接查找所有按钮并匹配文本（更简单有效）
        submit_clicked = False
        try:
            all_buttons = browser.eles('tag:button')
            for button in all_buttons:
                try:
                    button_text = button.text.strip()
                    if any(keyword in button_text for keyword in ["开始试用", "开始", "试用", "Submit", "Start"]):
                        logging.info(f"找到按钮: '{button_text}'")
                        time.sleep(0.2)  # 快速延迟
                        button.click()
                        logging.info("✅ 开始试用按钮已点击")
                        submit_clicked = True
                        break
                except:
                    continue
        except Exception as e:
            logging.error(f"查找按钮时出错: {e}")

        if submit_clicked:
            # 步骤10: 等待处理完成
            logging.info("10. 等待支付处理...")
            human_like_delay(2, 3)

            # 检查是否出现"处理中"状态
            processing_indicators = [
                'css:*:contains("处理中")',
                'css:*:contains("Processing")',
                'css:*:contains("正在处理")',
                'css:.loading',
                'css:.spinner'
            ]

            processing_detected = False
            for indicator_selector in processing_indicators:
                try:
                    processing_element = browser.ele(indicator_selector, timeout=3)
                    if processing_element:
                        logging.info("检测到处理中状态...")
                        processing_detected = True
                        break
                except:
                    continue

            # 等待处理完成，最多等待30秒
            max_wait_time = 30
            wait_interval = 1
            waited_time = 0
            success_detected = False

            while waited_time < max_wait_time:
                time.sleep(wait_interval)
                waited_time += wait_interval

                # 首先检查是否跳转到dashboard页面（最重要的成功标志）
                current_url = browser.url
                if "cursor.com" in current_url and "dashboard" in current_url:
                    logging.info("🎉 检测到页面跳转到dashboard - 支付成功！")
                    logging.info(f"当前URL: {current_url}")
                    success_detected = True
                    break

                # 检查是否出现绿色成功标志
                success_indicators = [
                    'css:*:contains("保存我的信息")',
                    'css:*:contains("以更快结账")',
                    'css:.success',
                    'css:.checkmark',
                    'css:*[style*="green"]',
                    'css:*[class*="success"]',
                    'css:*[style*="rgb(34, 197, 94)"]'  # 绿色
                ]

                for success_selector in success_indicators:
                    try:
                        success_element = browser.ele(success_selector, timeout=0.5)
                        if success_element:
                            logging.info("🎉 检测到成功标志！")
                            success_detected = True
                            break
                    except:
                        continue

                if success_detected:
                    break

                # 检查是否还在处理中
                still_processing = False
                for indicator_selector in processing_indicators:
                    try:
                        processing_element = browser.ele(indicator_selector, timeout=0.5)
                        if processing_element:
                            still_processing = True
                            break
                    except:
                        continue

                # 如果没有处理指示器且等待超过5秒，检查URL变化
                if not still_processing and waited_time > 5:
                    if current_url != browser.url:
                        logging.info(f"检测到URL变化: {browser.url}")
                        if "cursor.com" in browser.url:
                            logging.info("🎉 跳转到cursor.com - 支付成功！")
                            success_detected = True
                            break

                if waited_time % 5 == 0:  # 每5秒报告一次
                    logging.info(f"等待处理完成... ({waited_time}/{max_wait_time}秒) - 当前URL: {browser.url}")

            if not success_detected and waited_time >= max_wait_time:
                logging.warning("⚠️ 等待超时，检查最终状态...")
                final_url = browser.url
                if "cursor.com" in final_url:
                    logging.info("🎉 最终检查：已跳转到cursor.com - 支付成功！")
                    success_detected = True
                else:
                    logging.warning(f"最终URL: {final_url}")

            if success_detected:
                logging.info("✅ 支付处理成功完成！")
            else:
                logging.warning("⚠️ 未明确检测到成功标志，但流程已执行")

            # # 最终截图
            # browser.get_screenshot(path="drission_final_result.png")
            # logging.info("最终结果截图已保存: drission_final_result.png")

        logging.info("✅ 完整流程执行完成！")
        return True

    except Exception as e:
        logging.error(f"填写表单时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logging.info("=== Stripe DrissionPage 智能自动填写 ===")
    logging.info("✨ 使用内置自动填充功能，无需手动设置浏览器")

    # 从绑卡.txt读取URL
    try:
        with open("绑卡.txt", "r", encoding="utf-8") as f:
            payment_url = f.read().strip()
    except Exception as e:
        logging.error(f"读取绑卡.txt失败: {e}")
        return
    
    browser = None
    try:
        logging.info("=== Stripe DrissionPage 自动填写 ===\n")
        
        # 初始化浏览器
        browser = init_browser_with_patch()

        # 打开支付页面
        logging.info("正在打开支付页面...")
        browser.get(payment_url)

        # 设置自动填充信息
        setup_browser_autofill(browser)

        # # 截图保存初始状态
        # browser.get_screenshot(path="drission_initial.png")
        # logging.info("初始状态截图已保存")
        
        # 填写表单并提交
        if fill_stripe_form_drission(browser):
            logging.info("\n🎉 完整流程执行成功！")
            logging.info("✅ 表单填写完成")
            logging.info("✅ 提交按钮已点击")
            logging.info("✅ 支付处理完成")
        else:
            logging.error("\n❌ 流程执行失败")
            browser.get_screenshot(path="drission_failed.png")
        
        # 保持浏览器打开
        logging.info("\n浏览器将保持打开 120 秒，您可以检查结果或手动操作...")
        time.sleep(1120020)
        
    except KeyboardInterrupt:
        logging.info("检测到键盘中断，正在退出...")
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        if browser:
            browser.get_screenshot(path="drission_error.png")
            
    finally:
        if browser:
            try:
                browser.quit()
                logging.info("浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    main()
