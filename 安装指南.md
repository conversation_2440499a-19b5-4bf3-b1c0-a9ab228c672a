# Stripe Auto Fill Chrome 插件安装指南

## 快速安装步骤

### 第一步：准备图标文件

1. 在浏览器中打开 `create_icons.html` 文件
2. 点击"下载所有图标"按钮
3. 将下载的图标文件重命名为：
   - `icon16.png`
   - `icon48.png` 
   - `icon128.png`
4. 将这些图标文件放在插件文件夹中

### 第二步：安装插件

1. 打开 Chrome 浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择包含所有插件文件的文件夹
6. 插件安装完成！

### 第三步：使用插件

1. 导航到 Stripe 支付页面（如：`https://checkout.stripe.com/c/pay/...`）
2. 点击浏览器工具栏中的插件图标
3. 在弹出窗口中点击"自动填写表单"
4. 如需要，点击"自动提交"按钮
5. 如果出现验证码，手动完成后插件会自动重新提交

## 文件清单

确保您的插件文件夹包含以下文件：

```
📁 Stripe Auto Fill/
├── 📄 manifest.json          # 插件配置文件
├── 📄 content.js             # 主要功能脚本
├── 📄 popup.html             # 弹出窗口界面
├── 📄 popup.js               # 弹出窗口脚本
├── 📄 options.html           # 设置页面
├── 📄 styles.css             # 样式文件
├── 🖼️ icon16.png             # 16x16 图标
├── 🖼️ icon48.png             # 48x48 图标
├── 🖼️ icon128.png            # 128x128 图标
├── 📄 README.md              # 详细说明
├── 📄 安装指南.md            # 本文件
└── 📄 create_icons.html      # 图标生成器
```

## 预设卡信息

插件将自动填写以下信息：

| 字段 | 值 |
|------|-----|
| 卡号 | 4937 2420 1027 3546 |
| 有效期 | 06/28 |
| CVC | 124 |
| 姓名 | Canestro |
| 国家 | Canada |
| 地址 | 2225 137th Avenue |
| 城市 | Edmonton |
| 州/省 | Alberta |
| 邮编 | T5J 3P4 |

## 支持的网站

- ✅ `https://checkout.stripe.com/*`
- ✅ `https://buy.stripe.com/*`
- ✅ `*://*/c/pay/*`

## 常见问题

### Q: 插件无法加载怎么办？
A: 
1. 确认所有文件都在同一文件夹中
2. 检查是否开启了"开发者模式"
3. 确认图标文件存在且命名正确

### Q: 自动填写不工作怎么办？
A:
1. 确认当前页面是支持的 Stripe 域名
2. 刷新页面后重新尝试
3. 打开开发者工具查看控制台错误信息

### Q: 验证码检测不工作怎么办？
A:
1. 手动完成验证码
2. 等待3-5秒让插件检测
3. 如果仍未自动提交，可手动点击提交按钮

### Q: 如何修改预设的卡信息？
A:
1. 打开 `content.js` 文件
2. 找到 `CARD_INFO` 对象
3. 修改相应的值
4. 重新加载插件

## 安全提醒

- ⚠️ 此插件仅在本地运行，不会向外部发送数据
- ⚠️ 请仅在测试环境中使用
- ⚠️ 不要在生产环境中使用真实的信用卡信息
- ⚠️ 使用前请确保符合相关法律法规

## 技术支持

如果遇到问题：

1. 查看浏览器开发者工具的控制台错误信息
2. 确认页面结构是否与插件兼容
3. 尝试在不同的 Stripe 页面上测试

## 更新插件

如果需要更新插件：

1. 修改相应的文件
2. 在 `chrome://extensions/` 页面点击插件的"重新加载"按钮
3. 或者删除插件后重新安装

---

**祝您使用愉快！** 🎉
